"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Image, File, Search, X, Check, Loader2, Grid3X3, List, Filter, ChevronLeft, ChevronRight, ChevronDown, Upload, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { useDropzone } from "react-dropzone";
import { MediaItem } from "@/lib/services/MediaService";

interface MediaLibrarySelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (items: MediaItem[]) => void;
  multiple?: boolean;
  allowedTypes?: string[];
  maxFiles?: number;
}

export function MediaLibrarySelector({
  isOpen,
  onClose,
  onSelect,
  multiple = false,
  allowedTypes = ["*/*"], // Allow all file types
  maxFiles = 1,
}: MediaLibrarySelectorProps) {
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchInput, setSearchInput] = useState("");
  const [selectedItems, setSelectedItems] = useState<MediaItem[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [selectedFileType, setSelectedFileType] = useState<string>("all");
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [showUploadZone, setShowUploadZone] = useState(false);

  // File type filter options
  const fileTypeFilters = [
    { value: "all", label: "All Files", icon: File },
    { value: "image", label: "Images", icon: Image },
    { value: "pdf", label: "PDF Documents", icon: File },
    { value: "word", label: "Word Documents", icon: File },
    { value: "excel", label: "Excel Spreadsheets", icon: File },
    { value: "powerpoint", label: "PowerPoint Presentations", icon: File },
    { value: "video", label: "Videos", icon: File },
    { value: "audio", label: "Audio Files", icon: File },
    { value: "archive", label: "Archives", icon: File },
    { value: "code", label: "Code Files", icon: File },
    { value: "text", label: "Text Files", icon: File },
    { value: "other", label: "Other Files", icon: File },
  ];

  useEffect(() => {
    if (isOpen) {
      loadMedia();
    }
  }, [isOpen]);

  // Load media when page, search query, or file type changes
  useEffect(() => {
    if (isOpen) {
      loadMedia();
    }
  }, [currentPage, searchQuery, selectedFileType]);

  const loadMedia = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "20",
        sortBy: "created_at",
        sortOrder: "desc",
      });

      if (searchQuery.trim()) {
        params.append("search", searchQuery.trim());
      }

      if (selectedFileType !== "all") {
        const mimeTypeMap: Record<string, string> = {
          image: "image/*",
          pdf: "application/pdf",
          word: "application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          excel: "application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          powerpoint: "application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation",
          video: "video/*",
          audio: "audio/*",
          archive: "application/zip,application/x-rar-compressed,application/x-7z-compressed,application/x-tar,application/gzip",
          code: "application/javascript,text/html,text/css,application/json,text/x-python,text/x-java-source,text/x-c,text/x-c++,text/x-csharp,text/x-ruby,text/x-go,text/x-rust,text/x-swift,application/typescript,text/jsx,text/tsx,application/x-httpd-php,application/sql,application/x-sh",
          text: "text/plain,text/csv,application/xml,text/markdown,application/x-yaml",
        };
        
        if (mimeTypeMap[selectedFileType]) {
          params.append("mimeType", mimeTypeMap[selectedFileType]);
        }
      } else if (allowedTypes.length > 0) {
        const mimeTypes = allowedTypes.join(",");
        params.append("mimeType", mimeTypes);
      }

      const response = await fetch(`/api/media?${params}`);
      if (!response.ok) throw new Error("Failed to load media");

      const data = await response.json();
      setMediaItems(data.items);
      setTotalPages(data.totalPages);
      setTotalItems(data.total);
    } catch (err) {
      console.error("Error loading media:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleSearchInput = (query: string) => {
    setSearchInput(query);
  };

  const handleSearchSubmit = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setSearchQuery(searchInput);
      setCurrentPage(1);
    }
  };

  const handleFileTypeChange = (fileType: string) => {
    setSelectedFileType(fileType);
    setCurrentPage(1); // Reset to first page when changing file type
  };

  const handleItemClick = (item: MediaItem) => {
    if (multiple) {
      setSelectedItems(prev => {
        const isSelected = prev.some(selected => selected.id === item.id);
        if (isSelected) {
          return prev.filter(selected => selected.id !== item.id);
        } else {
          if (prev.length >= maxFiles) {
            return prev;
          }
          return [...prev, item];
        }
      });
    } else {
      // Single selection mode - toggle selection
      setSelectedItems(prev => {
        const isSelected = prev.some(selected => selected.id === item.id);
        if (isSelected) {
          return []; // Deselect if already selected
        } else {
          return [item]; // Select if not selected
        }
      });
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith("image/")) return <Image className="w-5 h-5" />;
    return <File className="w-5 h-5" />;
  };

  const uploadFiles = async (files: File[]) => {
    try {
      setUploading(true);
      setUploadProgress(0);

      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });

      const response = await fetch('/api/media/upload-with-save', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) throw new Error('Upload failed');

      const result = await response.json();
      const uploadedItems = result.data;
      
      // Convert the response format to MediaItem format
      const mediaItems = uploadedItems.map((item: any) => ({
        id: item.id,
        filename: item.filename,
        original_filename: item.filename,
        mime_type: item.mimeType,
        file_extension: item.filename.split('.').pop() || '',
        size: item.size,
        s3_url: item.url,
        width: item.width,
        height: item.height,
        duration: undefined,
        alternative_text: '',
        caption: '',
        folder_id: undefined,
        created_by: 'system',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));
      
      // Reset progress
      setUploadProgress(100);
      
      // Auto-select the uploaded files
      setSelectedItems(prev => [...prev, ...mediaItems]);
      
      // Refresh the media list to show the new uploads
      loadMedia();
      
    } catch (error) {
      console.error('Upload error:', error);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    uploadFiles(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    // If allowedTypes includes "*/*", don't set accept to allow all file types
    // Otherwise, use the specific allowed types
    accept: allowedTypes.includes("*/*") ? undefined : allowedTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    multiple: multiple,
    disabled: uploading,
  });

  const isItemSelected = (item: MediaItem) => {
    return selectedItems.some(selected => selected.id === item.id);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-none w-[90vw] h-[85vh] max-h-[85vh] overflow-hidden p-0 sm:max-w-none flex flex-col gap-0">
        {/* Header */}
        <DialogHeader className="px-6 py-5 border-b bg-gradient-to-r from-slate-50 to-gray-50">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl font-semibold text-gray-900">Media Library</DialogTitle>
              <p className="text-sm text-gray-500 mt-1">Select files from your media collection</p>
            </div>
            <div className="flex items-center gap-3">
              <Badge variant="secondary" className="text-sm px-3 py-1.5 bg-blue-100 text-blue-700 border-blue-200">
                {selectedItems.length} of {maxFiles} selected
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowUploadZone(!showUploadZone)}
                className="h-9 px-3"
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload Files
              </Button>
              <div className="flex items-center border rounded-lg bg-white shadow-sm">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="h-9 rounded-r-none border-r"
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="h-9 rounded-l-none"
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="flex flex-col h-full overflow-hidden">
          {/* Search and Actions Bar */}
          <div className="px-6 py-4 border-b bg-white shadow-sm">
            <div className="flex items-center gap-3">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  placeholder="Search by filename... (Press Enter to search)"
                  value={searchInput}
                  onChange={(e) => handleSearchInput(e.target.value)}
                  onKeyDown={handleSearchSubmit}
                  className="pl-11 h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="lg" className="h-11 border-gray-300 hover:bg-gray-50">
                    <Filter className="w-4 h-4 mr-2" />
                    {fileTypeFilters.find(f => f.value === selectedFileType)?.label || "All Files"}
                    <ChevronDown className="w-4 h-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  {fileTypeFilters.map((filter) => {
                    const IconComponent = filter.icon;
                    return (
                      <DropdownMenuItem
                        key={filter.value}
                        onClick={() => handleFileTypeChange(filter.value)}
                        className={`flex items-center gap-2 ${
                          selectedFileType === filter.value ? "bg-blue-50 text-blue-700" : ""
                        }`}
                      >
                        <IconComponent className="w-4 h-4" />
                        {filter.label}
                        {selectedFileType === filter.value && (
                          <Check className="w-4 h-4 ml-auto" />
                        )}
                      </DropdownMenuItem>
                    );
                  })}
                </DropdownMenuContent>
              </DropdownMenu>
              <Button 
                onClick={() => {
                  onSelect(selectedItems);
                  setSelectedItems([]);
                  onClose();
                }} 
                disabled={selectedItems.length === 0}
                size="lg"
                className="h-11 px-8 bg-blue-600 hover:bg-blue-700 shadow-sm"
              >
                <Check className="w-4 h-4 mr-2" />
                {selectedItems.length > 0 ? `Select ${selectedItems.length} file${selectedItems.length > 1 ? 's' : ''}` : 'Select Files'}
              </Button>
            </div>
          </div>

          {/* Selected Files Preview */}
          {selectedItems.length > 0 && (
            <div className="px-6 py-4 bg-blue-50/50 border-b border-blue-100">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-semibold text-blue-900">
                  Selected Files
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedItems([])}
                  className="text-blue-600 hover:text-blue-800 hover:bg-blue-100 h-7 px-3"
                >
                  Clear all
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {selectedItems.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center gap-2 bg-white rounded-lg px-3 py-2 border border-blue-200 shadow-sm hover:shadow transition-shadow group"
                  >
                    {item.mime_type.startsWith("image/") ? (
                      <img
                        src={item.s3_url}
                        alt={item.filename}
                        className="w-8 h-8 object-cover rounded"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-gradient-to-br from-gray-100 to-gray-200 rounded flex items-center justify-center">
                        {getFileIcon(item.mime_type)}
                      </div>
                    )}
                    <span className="truncate max-w-40 text-sm font-medium text-gray-700">{item.filename}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleItemClick(item)}
                      className="h-6 w-6 p-0 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Upload Area */}
          {showUploadZone && (
            <div className="px-6 py-4 border-b bg-white">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium text-gray-900">Upload New Files</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowUploadZone(false)}
                  className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                  isDragActive
                    ? "border-blue-400 bg-blue-50"
                    : "border-gray-300 hover:border-gray-400"
                } ${uploading ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                <input {...getInputProps()} />
                
                {uploading ? (
                  <div className="space-y-3">
                    <div className="w-12 h-12 mx-auto bg-blue-600 rounded-full flex items-center justify-center">
                      <Upload className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">Uploading...</p>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                      <Plus className="w-6 h-6 text-gray-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">
                        {isDragActive ? "Drop files here" : "Upload new files"}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Drag & drop files here, or click to browse
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Media Grid/List */}
          <div className="flex-1 overflow-auto bg-gradient-to-br from-gray-50 to-slate-50">
            <div className="p-6">
              {loading ? (
                <div className="flex items-center justify-center py-32">
                  <div className="flex flex-col items-center gap-4">
                    <Loader2 className="w-10 h-10 animate-spin text-blue-500" />
                    <p className="text-gray-600 font-medium">Loading your media files...</p>
                  </div>
                </div>
              ) : mediaItems.length === 0 ? (
                <div className="flex items-center justify-center py-32">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <File className="w-10 h-10 text-gray-400" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">No files found</h3>
                    <p className="text-gray-500">
                      {searchQuery ? "Try adjusting your search terms" : "Upload some files to get started"}
                    </p>
                  </div>
                </div>
              ) : (
                <div className={`grid gap-4 ${
                  viewMode === "grid" 
                    ? "grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
                    : "grid-cols-1"
                }`}>
                  {mediaItems.map((item) => (
                    <Card
                      key={item.id}
                      className={`cursor-pointer transition-all duration-200 hover:shadow-xl hover:-translate-y-1 border ${
                        isItemSelected(item)
                          ? "ring-2 ring-blue-500 shadow-xl border-blue-300 bg-blue-50/30"
                          : "hover:shadow-lg bg-white border-gray-200"
                      }`}
                      onClick={() => handleItemClick(item)}
                    >
                      <CardContent className={viewMode === "grid" ? "p-3" : "p-4 flex items-center gap-4"}>
                        <div className={`relative ${viewMode === "list" ? "flex-shrink-0" : ""}`}>
                          {/* Selection Indicator */}
                          {isItemSelected(item) && (
                            <div className="absolute -top-1 -right-1 z-10 bg-blue-600 text-white rounded-full p-1 shadow-lg ring-2 ring-white">
                              <Check className="w-3.5 h-3.5" />
                            </div>
                          )}

                          {/* File Preview */}
                          {item.mime_type.startsWith("image/") ? (
                            <div className="relative overflow-hidden rounded-lg">
                              <img
                                src={item.s3_url}
                                alt={item.filename}
                                className={`w-full object-cover transition-transform hover:scale-105 ${
                                  viewMode === "grid" ? "h-48" : "h-24 w-24"
                                }`}
                              />
                              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 hover:opacity-100 transition-opacity" />
                            </div>
                          ) : (
                            <div className={`bg-gradient-to-br from-gray-100 via-gray-50 to-gray-100 rounded-lg flex flex-col items-center justify-center border border-gray-200 ${
                              viewMode === "grid" ? "h-48" : "h-24 w-24"
                            }`}>
                              {getFileIcon(item.mime_type)}
                              <p className="text-xs text-gray-600 mt-2 font-semibold uppercase tracking-wide">
                                {item.file_extension}
                              </p>
                            </div>
                          )}
                        </div>

                        <div className={`mt-3 ${viewMode === "list" ? "flex-1 mt-0" : ""}`}>
                          <h4 className={`font-semibold truncate text-gray-900 ${
                            viewMode === "grid" ? "text-sm" : "text-base"
                          }`} title={item.filename}>
                            {item.filename}
                          </h4>
                          <div className={`flex items-center gap-2 mt-1.5 ${viewMode === "list" ? "mt-1" : ""}`}>
                            <p className="text-xs text-gray-500 font-medium">
                              {formatFileSize(item.size)}
                            </p>
                            {viewMode === "list" && (
                              <>
                                <span className="text-gray-300">•</span>
                                <Badge variant="outline" className="text-xs bg-gray-50">
                                  {item.mime_type.split('/')[0]}
                                </Badge>
                              </>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-8 pb-4">
                  <div className="text-sm text-gray-500">
                    Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, totalItems)} of {totalItems} files
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="h-9 px-3"
                    >
                      <ChevronLeft className="w-4 h-4 mr-1" />
                      Previous
                    </Button>
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(7, totalPages) }, (_, i) => {
                        let page;
                        if (totalPages <= 7) {
                          page = i + 1;
                        } else if (currentPage <= 4) {
                          page = i + 1;
                        } else if (currentPage >= totalPages - 3) {
                          page = totalPages - 6 + i;
                        } else {
                          page = currentPage - 3 + i;
                        }
                        
                        return (
                          <Button
                            key={page}
                            variant={currentPage === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(page)}
                            className={`w-9 h-9 p-0 ${
                              currentPage === page 
                                ? "bg-blue-600 hover:bg-blue-700" 
                                : "hover:bg-gray-100"
                            }`}
                          >
                            {page}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                      className="h-9 px-3"
                    >
                      Next
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}